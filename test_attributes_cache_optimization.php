<?php
/**
 * Тест за проверка на оптимизацията на кеширащия механизъм за атрибути
 * Този файл тества дали SQL заявките за атрибути са елиминирани
 */

// Зареждаме OpenCart framework
require_once('config.php');
require_once(DIR_SYSTEM . 'startup.php');

// Стартираме registry
$registry = new Registry();

// Зареждаме основните компоненти
$loader = new Loader($registry);
$registry->set('load', $loader);

$db = new DB(DB_DRIVER, DB_HOSTNAME, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);
$registry->set('db', $db);

$config = new Config();
$registry->set('config', $config);

// Зареждаме Multi Feed Syncer модела
$loader->model('extension/module/multi_feed_syncer');
$mfs_model = $registry->get('model_extension_module_multi_feed_syncer');

echo "=== ТЕСТ НА ОПТИМИЗАЦИЯТА НА КЕШИРАЩИЯ МЕХАНИЗЪМ ЗА АТРИБУТИ ===\n\n";

// Тестови атрибути - смесица от съществуващи и нови
$test_attributes = [
    'number_pages',
    'color_A39', 
    'A118',
    'A39',
    'razmeri__sh__d__v___cm',
    'teglo__kg',
    'seria',
    'softuer',
    'protsesor__seria',
    'pamet__gb',
    'test_new_attribute_1',
    'test_new_attribute_2',
    'test_new_attribute_3'
];

echo "Тестови атрибути: " . implode(', ', $test_attributes) . "\n\n";

// Изчистваме лога преди теста
$log_file = DIR_LOGS . 'mfs_attributes.log';
if (file_exists($log_file)) {
    file_put_contents($log_file, '');
}

echo "1. Започваме тест на кеширащия механизъм...\n";

// Извикваме тестовия метод
$test_result = $mfs_model->testAttributeCacheMechanism($test_attributes, 1, 1);

echo "\n2. Резултати от теста:\n";
echo "   - Общо тествани атрибути: " . $test_result['total_tested'] . "\n";
echo "   - Cache hits: " . $test_result['cache_hits'] . "\n";
echo "   - Cache misses: " . $test_result['cache_misses'] . "\n";
echo "   - Cache hit rate: " . $test_result['cache_hit_rate'] . "%\n";
echo "   - Резултати получени: " . $test_result['result_count'] . "\n";
echo "   - Размер на кеша: " . $test_result['cache_size'] . "\n";
echo "   - Статус на теста: " . $test_result['test_status'] . "\n\n";

echo "3. Проверяваме лога за SQL заявки...\n";

// Четем лога и търсим SELECT заявки за атрибути
$log_content = file_get_contents($log_file);
$lines = explode("\n", $log_content);

$select_queries = 0;
$problematic_queries = [];

foreach ($lines as $line) {
    if (strpos($line, 'SQL') !== false && strpos($line, 'EXECUTED') !== false) {
        $sql_start = strpos($line, 'EXECUTED): ') + 11;
        if ($sql_start > 11) {
            $sql_query = trim(substr($line, $sql_start));
            
            // Проверяваме за SELECT заявки към атрибути
            if (stripos($sql_query, 'SELECT') === 0 && 
                (stripos($sql_query, 'oc_attribute') !== false || 
                 stripos($sql_query, 'attribute_description') !== false)) {
                $select_queries++;
                $problematic_queries[] = $sql_query;
            }
        }
    }
}

echo "   - Намерени SELECT заявки за атрибути: $select_queries\n";

if ($select_queries > 0) {
    echo "\n❌ ПРОБЛЕМ: Все още има SELECT заявки за атрибути!\n";
    echo "Проблематични заявки:\n";
    foreach ($problematic_queries as $i => $query) {
        echo "   " . ($i + 1) . ". " . substr($query, 0, 100) . "...\n";
    }
} else {
    echo "\n✅ ОТЛИЧНО: Няма SELECT заявки за атрибути - оптимизацията работи!\n";
}

echo "\n4. Анализ на логовете:\n";

// Броим различните типове съобщения
$cache_hits = substr_count($log_content, 'CACHE HIT');
$cache_misses = substr_count($log_content, 'CACHE MISS');
$cache_updates = substr_count($log_content, 'CACHE UPDATE');
$preload_messages = substr_count($log_content, 'PRELOAD');

echo "   - Cache hit съобщения: $cache_hits\n";
echo "   - Cache miss съобщения: $cache_misses\n";
echo "   - Cache update съобщения: $cache_updates\n";
echo "   - Preload съобщения: $preload_messages\n";

echo "\n5. Заключение:\n";

if ($select_queries === 0 && $test_result['test_status'] === 'SUCCESS') {
    echo "✅ УСПЕХ: Оптимизацията работи перфектно!\n";
    echo "   - Няма повторни SELECT заявки за атрибути\n";
    echo "   - Кеширащият механизъм функционира правилно\n";
    echo "   - Всички атрибути са обработени успешно\n";
} else {
    echo "❌ ПРОБЛЕМ: Оптимизацията не работи напълно\n";
    if ($select_queries > 0) {
        echo "   - Все още има $select_queries SELECT заявки\n";
    }
    if ($test_result['test_status'] !== 'SUCCESS') {
        echo "   - Тестът не премина успешно\n";
    }
}

echo "\n=== КРАЙ НА ТЕСТА ===\n";

// Показваме последните 20 реда от лога за debug
echo "\nПоследни 20 реда от лога:\n";
echo "----------------------------------------\n";
$log_lines = explode("\n", $log_content);
$last_lines = array_slice($log_lines, -20);
foreach ($last_lines as $line) {
    if (!empty(trim($line))) {
        echo $line . "\n";
    }
}
echo "----------------------------------------\n";

?>
